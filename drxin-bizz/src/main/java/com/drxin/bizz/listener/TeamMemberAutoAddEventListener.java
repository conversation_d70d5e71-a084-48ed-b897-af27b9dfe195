package com.drxin.bizz.listener;

import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.TeamMemberAutoAddEvent;
import com.drxin.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 团队成员自动添加事件监听器
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Component
public class TeamMemberAutoAddEventListener {

    @Autowired
    private ITeamMemberService teamMemberService;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 异步处理团队成员自动添加事件
     * 
     * @param event 团队成员自动添加事件
     */
    @Async
    @EventListener
    public void handleTeamMemberAutoAdd(TeamMemberAutoAddEvent event) {
        try {
            log.info("接收到团队成员自动添加事件: teamId={}, leaderId={}, teamName={}", 
                event.getTeamId(), event.getLeaderId(), event.getTeamName());

            // 获取所有需要加入团队的用户
            List<SysUser> allMembers = new ArrayList<>();
            
            // 递归查找团长的下级，遇到disciple时截止搜索
            findAllSubordinates(event.getLeaderId(), allMembers, new HashSet<>());
            
            // 批量创建团队成员
            batchCreateTeamMembers(event.getTeamId(), allMembers);
            
            log.info("团队成员自动添加完成: teamId={}, 添加成员数量={}", 
                event.getTeamId(), allMembers.size());

        } catch (Exception e) {
            log.error("处理团队成员自动添加事件失败: teamId={}, leaderId={}", 
                event.getTeamId(), event.getLeaderId(), e);
        }
    }

    /**
     * 递归查找所有下级用户，遇到disciple时截止搜索
     * 
     * @param userId 用户ID
     * @param allMembers 所有成员集合
     * @param visited 已访问的用户ID集合，防止循环引用
     */
    private void findAllSubordinates(String userId, List<SysUser> allMembers, Set<String> visited) {
        if (StringUtils.isEmpty(userId) || visited.contains(userId)) {
            return;
        }
        
        visited.add(userId);
        
        // 查询当前用户的直接下级
        List<SysUser> subordinates = sysUserMapper.selectUsersByInviterId(userId);
        
        for (SysUser subordinate : subordinates) {
            if (!visited.contains(subordinate.getUserId().toString())) {
                // 检查用户类型是否包含disciple
                if (containsDisciple(subordinate.getUserType())) {
                    // 如果包含disciple，不加入团队，也不继续搜索其下级
                    log.debug("用户包含disciple身份，跳过: userId={}, userType={}", 
                        subordinate.getUserId(), subordinate.getUserType());
                    continue;
                }
                
                // 不包含disciple，加入团队成员列表
                allMembers.add(subordinate);
                
                // 继续递归查找下级的下级
                findAllSubordinates(subordinate.getUserId().toString(), allMembers, visited);
            }
        }
    }

    /**
     * 检查用户类型是否包含disciple
     * 
     * @param userType 用户类型字符串
     * @return 是否包含disciple
     */
    private boolean containsDisciple(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return false;
        }
        
        // userType是逗号分隔的字符串，检查是否包含disciple
        String[] userTypes = userType.split(",");
        for (String type : userTypes) {
            if ("disciple".equals(type.trim())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 批量创建团队成员
     * 
     * @param teamId 团队ID
     * @param users 用户列表
     */
    private void batchCreateTeamMembers(String teamId, List<SysUser> users) {
        if (users.isEmpty()) {
            log.info("没有需要添加的团队成员: teamId={}", teamId);
            return;
        }
        
        List<TeamMember> teamMembers = new ArrayList<>();
        
        for (SysUser user : users) {
            TeamMember teamMember = new TeamMember();
            teamMember.setMemberId(user.getUserId().toString());
            teamMember.setTeamId(teamId);
            teamMember.setMemberName(StringUtils.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getNickName());
            teamMember.setMemberType(mapUserTypeToMemberType(user.getUserType()));
            teamMember.setCreateTime(DateUtils.getNowDate());
            
            teamMembers.add(teamMember);
        }
        
        // 批量插入团队成员
        int successCount = 0;
        for (TeamMember teamMember : teamMembers) {
            try {
                teamMemberService.insertTeamMember(teamMember);
                successCount++;
            } catch (Exception e) {
                log.error("插入团队成员失败: memberId={}, teamId={}", 
                    teamMember.getMemberId(), teamMember.getTeamId(), e);
            }
        }
        
        log.info("批量创建团队成员完成: teamId={}, 总数={}, 成功={}", 
            teamId, teamMembers.size(), successCount);
    }

    /**
     * 将用户类型映射为团队成员类型
     * 
     * @param userType 用户类型
     * @return 团队成员类型
     */
    private String mapUserTypeToMemberType(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return "普通成员";
        }
        
        // userType是逗号分隔的字符串，取第一个有效的类型
        String[] userTypes = userType.split(",");
        for (String type : userTypes) {
            String trimmedType = type.trim();
            switch (trimmedType) {
                case "aider":
                    return "急救员";
                case "mentor":
                    return "导师";
                case "disciple":
                    return "弟子";
                case "general":
                default:
                    return "普通成员";
            }
        }
        
        return "普通成员";
    }
}
