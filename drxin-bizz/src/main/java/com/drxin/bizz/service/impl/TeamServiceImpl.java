package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.drxin.bizz.mapper.TeamMapper;
import com.drxin.bizz.domain.Team;
import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.service.ITeamService;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.system.mapper.SysUserMapper;
import com.drxin.common.core.domain.entity.SysUser;

import javax.annotation.Resource;

/**
 * 团队管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class TeamServiceImpl extends ServiceImpl<TeamMapper, Team>  implements ITeamService {
    @Resource
    private TeamMapper teamMapper;

    @Autowired
    private ITeamMemberService teamMemberService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询团队管理
     * 
     * @param teamId 团队管理主键
     * @return 团队管理
     */
    @Override
    public Team selectTeamByTeamId(Long teamId) {
        return teamMapper.selectTeamByTeamId(teamId);
    }

    /**
     * 查询团队管理列表
     * 
     * @param team 团队管理
     * @return 团队管理
     */
    @Override
    public List<Team> selectTeamList(Team team) {
        return teamMapper.selectTeamList(team);
    }

    /**
     * 新增团队管理
     *
     * @param team 团队管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTeam(Team team) {
        team.setCreateTime(DateUtils.getNowDate());
        int result = teamMapper.insertTeam(team);

        // 团队创建成功后，自动添加团队成员
        if (result > 0 && StringUtils.isNotEmpty(team.getLeaderId())) {
            addTeamMembersAfterTeamCreation(team.getTeamId().toString(), team.getLeaderId());
        }

        return result;
    }

    /**
     * 修改团队管理
     * 
     * @param team 团队管理
     * @return 结果
     */
    @Override
    public int updateTeam(Team team) {
        return teamMapper.updateTeam(team);
    }

    /**
     * 批量删除团队管理
     * 
     * @param teamIds 需要删除的团队管理主键
     * @return 结果
     */
    @Override
    public int deleteTeamByTeamIds(Long[] teamIds) {
        return teamMapper.deleteTeamByTeamIds(teamIds);
    }

    /**
     * 删除团队管理信息
     *
     * @param teamId 团队管理主键
     * @return 结果
     */
    @Override
    public int deleteTeamByTeamId(Long teamId) {
        return teamMapper.deleteTeamByTeamId(teamId);
    }

    /**
     * 团队创建后自动添加团队成员
     *
     * @param teamId 团队ID
     * @param leaderId 团长ID
     */
    private void addTeamMembersAfterTeamCreation(String teamId, String leaderId) {
        // 获取所有需要加入团队的用户
        List<SysUser> allMembers = new ArrayList<>();

        // 递归查找团长的下级，遇到disciple时截止搜索
        findAllSubordinates(leaderId, allMembers, new HashSet<>());

        // 批量创建团队成员
        batchCreateTeamMembers(teamId, allMembers);
    }

    /**
     * 递归查找所有下级用户
     *
     * @param userId 用户ID
     * @param allMembers 所有成员集合
     * @param visited 已访问的用户ID集合，防止循环引用
     */
    private void findAllSubordinates(String userId, Set<SysUser> allMembers, Set<String> visited) {
        if (StringUtils.isEmpty(userId) || visited.contains(userId)) {
            return;
        }

        visited.add(userId);

        // 查询当前用户的直接下级
        List<SysUser> subordinates = sysUserMapper.selectUsersByInviterId(userId);

        for (SysUser subordinate : subordinates) {
            if (!visited.contains(subordinate.getUserId().toString())) {
                allMembers.add(subordinate);
                // 递归查找下级的下级
                findAllSubordinates(subordinate.getUserId().toString(), allMembers, visited);
            }
        }
    }

    /**
     * 过滤掉userType包含disciple的用户
     *
     * @param users 用户列表
     * @return 过滤后的用户列表
     */
    private List<SysUser> filterNonDiscipleUsers(List<SysUser> users) {
        List<SysUser> filteredUsers = new ArrayList<>();

        for (SysUser user : users) {
            String userType = user.getUserType();
            if (StringUtils.isNotEmpty(userType)) {
                // userType是逗号分隔的字符串，检查是否包含disciple
                String[] userTypes = userType.split(",");
                boolean containsDisciple = false;
                for (String type : userTypes) {
                    if ("disciple".equals(type.trim())) {
                        containsDisciple = true;
                        break;
                    }
                }

                // 如果不包含disciple，则加入过滤后的列表
                if (!containsDisciple) {
                    filteredUsers.add(user);
                }
            } else {
                // 如果userType为空，也加入列表
                filteredUsers.add(user);
            }
        }

        return filteredUsers;
    }

    /**
     * 批量创建团队成员
     *
     * @param teamId 团队ID
     * @param users 用户列表
     */
    private void batchCreateTeamMembers(String teamId, List<SysUser> users) {
        if (users.isEmpty()) {
            return;
        }

        List<TeamMember> teamMembers = new ArrayList<>();

        for (SysUser user : users) {
            TeamMember teamMember = new TeamMember();
            teamMember.setMemberId(user.getUserId().toString());
            teamMember.setTeamId(teamId);
            teamMember.setMemberName(StringUtils.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getNickName());
            teamMember.setMemberType(mapUserTypeToMemberType(user.getUserType()));
            teamMember.setCreateTime(DateUtils.getNowDate());

            teamMembers.add(teamMember);
        }

        // 批量插入团队成员
        for (TeamMember teamMember : teamMembers) {
            teamMemberService.insertTeamMember(teamMember);
        }
    }

    /**
     * 将用户类型映射为团队成员类型
     *
     * @param userType 用户类型
     * @return 团队成员类型
     */
    private String mapUserTypeToMemberType(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return "普通成员";
        }

        // userType是逗号分隔的字符串，取第一个有效的类型
        String[] userTypes = userType.split(",");
        for (String type : userTypes) {
            String trimmedType = type.trim();
            switch (trimmedType) {
                case "aider":
                    return "急救员";
                case "mentor":
                    return "导师";
                case "disciple":
                    return "弟子";
                case "general":
                default:
                    return "普通成员";
            }
        }

        return "普通成员";
    }
}
