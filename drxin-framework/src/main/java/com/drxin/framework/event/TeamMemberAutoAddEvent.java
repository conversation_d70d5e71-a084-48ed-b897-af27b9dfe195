package com.drxin.framework.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 团队成员自动添加事件
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Getter
public class TeamMemberAutoAddEvent extends ApplicationEvent {
    
    /** 团队ID */
    private final String teamId;
    
    /** 团长ID */
    private final String leaderId;
    
    /** 团队名称 */
    private final String teamName;

    public TeamMemberAutoAddEvent(Object source, String teamId, String leaderId, String teamName) {
        super(source);
        this.teamId = teamId;
        this.leaderId = leaderId;
        this.teamName = teamName;
    }
}
